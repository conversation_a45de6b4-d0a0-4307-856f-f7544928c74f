/**
 * Performance test for the LettersBot solver
 *
 * This script tests the solver's performance on various board configurations
 * and validates that it can find reasonable solutions.
 */

import { Board } from '../lib/models/Board';
import { Tile } from '../lib/models/Tile';
import { GameState } from '../lib/models/GameState';
import { bestWordsFast } from '../lib/bestWordsFast';
import { upperBoundForBoard } from '../lib/solver/optimization';
import { hashBoard } from '../lib/solver/hashing';
import { OPTIMIZED_PARAMETERS } from '../lib/solver/parameterOptimization';

async function main() {
	console.log('🎯 LettersBot Solver Performance Test');
	console.log('=====================================\n');

	try {
		// Load dictionary
		console.log('📚 Loading dictionary...');
		const fs = await import('fs');
		const path = await import('path');

		const dictPath = path.join(process.cwd(), 'src/lib/dict.bin');
		const dictData = fs.readFileSync(dictPath);
		const { loadDictionary } = await import('../lib/utils/dictionary');
		await loadDictionary(dictData.buffer);
		console.log('✅ Dictionary loaded successfully\n');

		// Test 1: Basic word finding
		await testWordFinding();

		// Test 2: Upper bound calculation
		await testUpperBounds();

		// Test 3: Board hashing performance
		await testHashingPerformance();

		// Test 4: Parameter validation
		await testParameterSets();

		// Test 5: Game state simulation
		await testGameStateSimulation();

		console.log('\n🎉 All performance tests completed successfully!');
	} catch (error) {
		console.error('💥 Performance test failed:', error);
		process.exit(1);
	}
}

async function testWordFinding(): Promise<void> {
	console.log('🔍 Testing word finding capabilities...');

	// Create a test board with known good letters
	const testBoard = createTestBoard();

	console.log('📋 Test board:');
	console.log(testBoard.toString());
	console.log();

	// Find best words
	const startTime = Date.now();
	const words = bestWordsFast(testBoard, 20);
	const endTime = Date.now();

	console.log(`⏱️  Found ${words.length} words in ${endTime - startTime}ms`);

	if (words.length === 0) {
		throw new Error('No words found - this suggests an issue with word finding');
	}

	// Display top words
	console.log('🏆 Top words found:');
	words.slice(0, 10).forEach((word, i) => {
		console.log(`   ${i + 1}. ${word.letters} (score: ${word.score})`);
	});

	console.log('✅ Word finding test passed\n');
}

async function testUpperBounds(): Promise<void> {
	console.log('📊 Testing upper bound calculations...');

	const boards = [
		createTestBoard(),
		Board.createRandom(),
		Board.createRandom(),
		createHighValueBoard(),
		createLowValueBoard()
	];

	for (let i = 0; i < boards.length; i++) {
		const board = boards[i];
		const remainingTurns = 3;

		const startTime = Date.now();
		const upperBound = upperBoundForBoard(board, remainingTurns);
		const endTime = Date.now();

		console.log(`   Board ${i + 1}: Upper bound = ${upperBound} (${endTime - startTime}ms)`);

		if (upperBound < 0) {
			throw new Error(`Invalid upper bound: ${upperBound}`);
		}

		if (upperBound === 0 && remainingTurns > 0) {
			console.warn(`   Warning: Zero upper bound with ${remainingTurns} turns remaining`);
		}
	}

	console.log('✅ Upper bound test passed\n');
}

async function testHashingPerformance(): Promise<void> {
	console.log('🔗 Testing hashing performance...');

	const boards: Board[] = [];
	for (let i = 0; i < 1000; i++) {
		boards.push(Board.createRandom());
	}

	const startTime = Date.now();
	const hashes = boards.map((board) => hashBoard(board));
	const endTime = Date.now();

	const uniqueHashes = new Set(hashes).size;
	const collisionRate = (boards.length - uniqueHashes) / boards.length;
	const avgTime = (endTime - startTime) / boards.length;

	console.log(`   Hashed ${boards.length} boards in ${endTime - startTime}ms`);
	console.log(`   Average time per hash: ${avgTime.toFixed(3)}ms`);
	console.log(`   Unique hashes: ${uniqueHashes}/${boards.length}`);
	console.log(`   Collision rate: ${(collisionRate * 100).toFixed(2)}%`);

	if (avgTime > 1) {
		throw new Error(`Hashing too slow: ${avgTime}ms per board`);
	}

	if (collisionRate > 0.1) {
		throw new Error(`Too many hash collisions: ${collisionRate * 100}%`);
	}

	console.log('✅ Hashing performance test passed\n');
}

async function testParameterSets(): Promise<void> {
	console.log('⚙️  Testing parameter sets...');

	const parameterSets = [
		OPTIMIZED_PARAMETERS.FAST,
		OPTIMIZED_PARAMETERS.BALANCED,
		OPTIMIZED_PARAMETERS.QUALITY,
		OPTIMIZED_PARAMETERS.MEMORY_EFFICIENT
	];

	for (const [name, params] of Object.entries(OPTIMIZED_PARAMETERS)) {
		console.log(`   Testing ${name} parameters...`);

		// Validate parameters
		const { validateParameters } = await import('../lib/solver/parameterOptimization');
		const validation = validateParameters(params);

		if (!validation.valid) {
			throw new Error(`Invalid ${name} parameters: ${validation.errors.join(', ')}`);
		}

		if (validation.warnings.length > 0) {
			console.log(`     Warnings: ${validation.warnings.join(', ')}`);
		}

		console.log(`     ✅ ${name} parameters valid`);
	}

	console.log('✅ Parameter validation test passed\n');
}

async function testGameStateSimulation(): Promise<void> {
	console.log('🎮 Testing game state simulation...');

	const initialBoard = createTestBoard();
	let gameState = GameState.fromBoard(initialBoard);

	console.log(`   Initial state: Turn ${gameState.turn}, Score ${gameState.total}`);

	// Simulate a few moves
	for (let turn = 0; turn < 3; turn++) {
		if (!gameState.canPlayMove()) break;

		// Find a word to play
		const words = bestWordsFast(gameState.board, 5);
		if (words.length === 0) {
			console.log(`     No words found for turn ${turn + 1}`);
			break;
		}

		// Play the best word (with dummy positions)
		const bestWord = words[0];
		const wordWithPositions = bestWord.clone();
		// Add dummy positions for testing
		wordWithPositions.positions.length = 0;
		for (let i = 0; i < bestWord.letters.length; i++) {
			wordWithPositions.positions.push([0, i]);
		}

		try {
			gameState = gameState.playMove(wordWithPositions);
			console.log(
				`     Turn ${gameState.turn}: Played "${bestWord.letters}" for ${bestWord.score} points (Total: ${gameState.total})`
			);
		} catch (error) {
			console.log(`     Turn ${turn + 1}: Could not play "${bestWord.letters}" - ${error}`);
			break;
		}
	}

	console.log(`   Final state: Turn ${gameState.turn}, Score ${gameState.total}`);
	console.log('✅ Game state simulation test passed\n');
}

function createTestBoard(): Board {
	// Create a board with some good letters for word formation
	const tiles: Tile[][] = [];
	const letters = [
		['C', 'A', 'T', 'S', 'E'],
		['H', 'O', 'U', 'S', 'E'],
		['W', 'O', 'R', 'D', 'S'],
		['G', 'A', 'M', 'E', 'S'],
		['T', 'E', 'S', 'T', 'S']
	];

	for (let row = 0; row < 5; row++) {
		tiles[row] = [];
		for (let col = 0; col < 5; col++) {
			// Add some multipliers for testing
			let letterMult: 1 | 2 | 3 = 1;
			let wordMult: 1 | 2 | 3 = 1;

			if ((row === 0 || row === 4) && (col === 0 || col === 4)) {
				wordMult = 2; // Corners get word multipliers
			}
			if (row === 2 && col === 2) {
				letterMult = 3; // Center gets letter multiplier
			}

			tiles[row][col] = new Tile(
				letters[row][col],
				row as 0 | 1 | 2 | 3 | 4,
				col as 0 | 1 | 2 | 3 | 4,
				letterMult,
				wordMult
			);
		}
	}

	return new Board(tiles);
}

function createHighValueBoard(): Board {
	// Create a board with high-value letters
	const tiles: Tile[][] = [];
	const highValueLetters = [
		'Q',
		'X',
		'Z',
		'J',
		'K',
		'Q',
		'X',
		'Z',
		'J',
		'K',
		'Q',
		'X',
		'Z',
		'J',
		'K',
		'Q',
		'X',
		'Z',
		'J',
		'K',
		'Q',
		'X',
		'Z',
		'J',
		'K'
	];

	let letterIndex = 0;
	for (let row = 0; row < 5; row++) {
		tiles[row] = [];
		for (let col = 0; col < 5; col++) {
			tiles[row][col] = new Tile(
				highValueLetters[letterIndex++],
				row as 0 | 1 | 2 | 3 | 4,
				col as 0 | 1 | 2 | 3 | 4,
				3, // High letter multiplier
				3 // High word multiplier
			);
		}
	}

	return new Board(tiles);
}

function createLowValueBoard(): Board {
	// Create a board with low-value letters
	const tiles: Tile[][] = [];

	for (let row = 0; row < 5; row++) {
		tiles[row] = [];
		for (let col = 0; col < 5; col++) {
			tiles[row][col] = new Tile(
				'A', // All A's (low value)
				row as 0 | 1 | 2 | 3 | 4,
				col as 0 | 1 | 2 | 3 | 4,
				1, // No multipliers
				1
			);
		}
	}

	return new Board(tiles);
}

// Run the performance tests
main().catch((error) => {
	console.error('💥 Unexpected error:', error);
	process.exit(1);
});
