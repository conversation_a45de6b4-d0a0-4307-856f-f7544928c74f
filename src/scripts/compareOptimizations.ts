/**
 * Compare performance between original and optimized bestWords implementations
 */

import { Board } from '../lib/models/Board';
import { Tile } from '../lib/models/Tile';
import { bestWords } from '../lib/bestWords';
import { bestWordsOptimized } from '../lib/bestWordsOptimized';
import { bestWordsFast } from '../lib/bestWordsFast';

// Profile a function
function profile<T>(name: string, fn: () => T): { result: T; time: number } {
	const start = performance.now();
	const result = fn();
	const end = performance.now();
	const time = end - start;
	console.log(`⏱️  ${name}: ${time.toFixed(2)}ms`);
	return { result, time };
}

function createTestBoard(): Board {
	const tiles = [
		['C', 'A', 'T', 'S', 'E'],
		['H', 'O', 'U', 'S', 'E'],
		['W', 'O', 'R', 'D', 'S'],
		['G', 'A', 'M', 'E', 'S'],
		['T', 'E', 'S', 'T', 'S']
	];

	const board = new Board();
	for (let row = 0; row < 5; row++) {
		for (let col = 0; col < 5; col++) {
			const tile = new Tile(tiles[row][col], row, col);
			// Add some multipliers for realism
			if ((row + col) % 3 === 0) tile.letterMult = 2;
			if ((row + col) % 7 === 0) tile.wordMult = 2;
			board.setTile(row, col, tile);
		}
	}
	return board;
}

function createRandomBoard(): Board {
	const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
	const board = new Board();

	for (let row = 0; row < 5; row++) {
		for (let col = 0; col < 5; col++) {
			const randomLetter = letters[Math.floor(Math.random() * letters.length)];
			const tile = new Tile(randomLetter, row, col);

			// Random multipliers
			if (Math.random() < 0.2) tile.letterMult = 2;
			if (Math.random() < 0.1) tile.wordMult = 2;
			if (Math.random() < 0.05) tile.letterMult = 3;
			if (Math.random() < 0.02) tile.wordMult = 3;

			board.setTile(row, col, tile);
		}
	}
	return board;
}

async function main() {
	console.log('🚀 Performance Comparison: Original vs Optimized');
	console.log('================================================\n');

	const testBoards = [
		{ name: 'Test Board', board: createTestBoard() },
		{ name: 'Random Board 1', board: createRandomBoard() },
		{ name: 'Random Board 2', board: createRandomBoard() },
		{ name: 'Random Board 3', board: createRandomBoard() }
	];

	const testCases = [10, 25, 50, 100];

	for (const { name, board } of testBoards) {
		console.log(`📋 Testing with ${name}:`);
		console.log('Board:');
		for (let row = 0; row < 5; row++) {
			const rowStr = board.tiles[row].map((t) => t.letter).join(' ');
			console.log(`   ${rowStr}`);
		}
		console.log();

		for (const K of testCases) {
			console.log(`  🎯 K = ${K}:`);

			// Test ultra-fast version first (should be fastest)
			const ultraFastResult = profile(`    Ultra-Fast (K=${K})`, () => {
				return bestWordsFast(board, K);
			});

			console.log(`       Found ${ultraFastResult.result.length} words`);
			console.log(
				`       Top 5 scores: ${ultraFastResult.result
					.slice(0, 5)
					.map((w) => w.score)
					.join(', ')}`
			);

			// Test optimized version
			const optimizedResult = profile(`    Optimized (K=${K})`, () => {
				return bestWordsOptimized(board, K);
			});

			console.log(`       Found ${optimizedResult.result.length} words`);
			console.log(
				`       Top 5 scores: ${optimizedResult.result
					.slice(0, 5)
					.map((w) => w.score)
					.join(', ')}`
			);

			// Test original version with timeout
			console.log(`    Original (K=${K}): Testing with 10s timeout...`);
			const timeoutMs = 10000; // 10 second timeout
			let originalResult: any = null;
			let originalTime = 0;
			let timedOut = false;

			try {
				const startTime = performance.now();
				const timeoutId = setTimeout(() => {
					timedOut = true;
					throw new Error('Timeout');
				}, timeoutMs);

				originalResult = bestWords(board, K);
				clearTimeout(timeoutId);
				originalTime = performance.now() - startTime;

				if (!timedOut) {
					console.log(`⏱️      Original (K=${K}): ${originalTime.toFixed(2)}ms`);
					console.log(`       Found ${originalResult.length} words`);
					console.log(
						`       Top 5 scores: ${originalResult
							.slice(0, 5)
							.map((w: any) => w.score)
							.join(', ')}`
					);

					// Calculate speedup vs ultra-fast and optimized
					const speedupVsUltraFast = originalTime / ultraFastResult.time;
					const speedupVsOptimized = originalTime / optimizedResult.time;
					console.log(`       🚀 Speedup vs Ultra-Fast: ${speedupVsUltraFast.toFixed(1)}x faster`);
					console.log(`       🚀 Speedup vs Optimized: ${speedupVsOptimized.toFixed(1)}x faster`);
				}
			} catch (error) {
				console.log(`⏱️      Original (K=${K}): >10000ms (TIMEOUT)`);
				console.log(
					`       🚀 Speedup vs Ultra-Fast: >${(timeoutMs / ultraFastResult.time).toFixed(0)}x faster`
				);
				console.log(
					`       🚀 Speedup vs Optimized: >${(timeoutMs / optimizedResult.time).toFixed(0)}x faster`
				);
			}

			console.log();
		}
		console.log('─'.repeat(50));
	}

	// Memory usage comparison
	console.log('\n💾 Memory Usage Analysis:');
	const memBefore = process.memoryUsage();
	console.log(`   Memory before: ${(memBefore.heapUsed / 1024 / 1024).toFixed(2)} MB`);

	// Run ultra-fast version multiple times
	const board = createTestBoard();
	for (let i = 0; i < 10; i++) {
		bestWordsFast(board, 50);
	}

	const memAfterUltraFast = process.memoryUsage();
	console.log(
		`   Memory after 10 ultra-fast runs: ${(memAfterUltraFast.heapUsed / 1024 / 1024).toFixed(2)} MB`
	);
	console.log(
		`   Memory increase (ultra-fast): ${((memAfterUltraFast.heapUsed - memBefore.heapUsed) / 1024 / 1024).toFixed(2)} MB`
	);

	// Run optimized version multiple times
	for (let i = 0; i < 10; i++) {
		bestWordsOptimized(board, 50);
	}

	const memAfter = process.memoryUsage();
	console.log(
		`   Memory after 10 optimized runs: ${(memAfter.heapUsed / 1024 / 1024).toFixed(2)} MB`
	);
	console.log(
		`   Memory increase (optimized): ${((memAfter.heapUsed - memAfterUltraFast.heapUsed) / 1024 / 1024).toFixed(2)} MB`
	);

	console.log('\n✅ Performance comparison complete');
}

main().catch(console.error);
