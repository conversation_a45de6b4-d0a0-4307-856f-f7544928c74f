/**
 * Integration tests for bestWordsFast function
 *
 * Tests how the function integrates with the broader game system
 */

import { Board } from '../models/Board';
import { GameState } from '../models/GameState';
import { bestWordsFast } from '../bestWordsFast';
import { bestWords } from '../bestWords';
import {
	calculateAccurateUpperBound,
	UpperBoundStrategy,
	calculateUpperBoundWithStrategy
} from '../solver/optimization';

interface IntegrationTestResult {
	name: string;
	passed: boolean;
	error?: string;
	details?: any;
}

/**
 * Test basic Word object structure and validity
 */
function testWordObjectStructure(): IntegrationTestResult {
	try {
		const board = Board.createRandom();

		// Get results from bestWordsFast
		const words = bestWordsFast(board, 10);

		if (words.length === 0) {
			return {
				name: 'Word Object Structure',
				passed: true,
				details: { message: 'No words found (acceptable for some boards)' }
			};
		}

		// Check that all words have valid structure
		for (const word of words) {
			if (!word.letters || typeof word.letters !== 'string') {
				return {
					name: 'Word Object Structure',
					passed: false,
					error: `Invalid letters property: ${word.letters}`
				};
			}

			if (typeof word.score !== 'number' || word.score < 0) {
				return {
					name: 'Word Object Structure',
					passed: false,
					error: `Invalid score: ${word.score}`
				};
			}

			if (!Array.isArray(word.positions)) {
				return {
					name: 'Word Object Structure',
					passed: false,
					error: `Invalid positions: ${word.positions}`
				};
			}

			if (word.positions.length !== word.letters.length) {
				return {
					name: 'Word Object Structure',
					passed: false,
					error: `Positions length (${word.positions.length}) doesn't match letters length (${word.letters.length})`
				};
			}

			// Check position validity
			for (const [row, col] of word.positions) {
				if (typeof row !== 'number' || typeof col !== 'number') {
					return {
						name: 'Word Object Structure',
						passed: false,
						error: `Invalid position coordinates: [${row}, ${col}]`
					};
				}

				if (row < 0 || row >= 5 || col < 0 || col >= 5) {
					return {
						name: 'Word Object Structure',
						passed: false,
						error: `Position out of bounds: [${row}, ${col}]`
					};
				}
			}
		}

		// Check that scores are reasonable
		const topScore = words[0].score;
		if (topScore > 1000) {
			return {
				name: 'Word Object Structure',
				passed: false,
				error: `Unreasonably high score: ${topScore}`
			};
		}

		return {
			name: 'Word Object Structure',
			passed: true,
			details: {
				wordsFound: words.length,
				topScore,
				averageScore: Math.round(words.reduce((sum, w) => sum + w.score, 0) / words.length),
				wordLengths: words.map((w) => w.letters.length)
			}
		};
	} catch (error) {
		return {
			name: 'Word Object Structure',
			passed: false,
			error: String(error)
		};
	}
}

/**
 * Test integration with GameState
 */
function testGameStateIntegration(): IntegrationTestResult {
	try {
		const board = Board.createRandom();
		const gameState = new GameState(board, 0, 0, []);

		// Get words from bestWordsFast
		const words = bestWordsFast(board, 5);

		if (words.length === 0) {
			return {
				name: 'GameState Integration',
				passed: true,
				details: { message: 'No words found (acceptable for some boards)' }
			};
		}

		// Try to play the best word
		const bestWord = words[0];

		// Check if the word can be played (basic validation)
		if (!bestWord.letters || bestWord.letters.length === 0) {
			return {
				name: 'GameState Integration',
				passed: false,
				error: 'Best word has no letters'
			};
		}

		if (!bestWord.positions || bestWord.positions.length !== bestWord.letters.length) {
			return {
				name: 'GameState Integration',
				passed: false,
				error: "Word positions don't match letters length"
			};
		}

		// Validate positions are on the board
		for (const [row, col] of bestWord.positions) {
			if (row < 0 || row >= 5 || col < 0 || col >= 5) {
				return {
					name: 'GameState Integration',
					passed: false,
					error: `Invalid position [${row}, ${col}]`
				};
			}
		}

		// Try to create a new game state (this tests basic compatibility)
		try {
			const newState = gameState.playMove(bestWord);

			// Basic validation of new state
			if (newState.turn !== gameState.turn + 1) {
				return {
					name: 'GameState Integration',
					passed: false,
					error: 'Turn not incremented correctly'
				};
			}

			if (newState.total < gameState.total) {
				return {
					name: 'GameState Integration',
					passed: false,
					error: 'Total score decreased'
				};
			}
		} catch (error) {
			// This might fail due to adjacency requirements or other game rules
			// but the word structure should still be valid
			console.log(`Note: GameState.playMove failed (may be expected): ${error}`);
		}

		return {
			name: 'GameState Integration',
			passed: true,
			details: {
				wordsFound: words.length,
				bestWordLetters: bestWord.letters,
				bestWordScore: bestWord.score,
				bestWordPositions: bestWord.positions.length
			}
		};
	} catch (error) {
		return {
			name: 'GameState Integration',
			passed: false,
			error: String(error)
		};
	}
}

/**
 * Test performance under realistic usage scenarios
 */
function testRealisticPerformance(): IntegrationTestResult {
	try {
		const results: { boardType: string; time: number; wordsFound: number }[] = [];

		// Test different board scenarios
		const scenarios = [
			{ name: 'Random Board 1', board: Board.createRandom() },
			{ name: 'Random Board 2', board: Board.createRandom() },
			{ name: 'Random Board 3', board: Board.createRandom() }
		];

		for (const scenario of scenarios) {
			const startTime = performance.now();
			const words = bestWordsFast(scenario.board, 25); // Realistic K value
			const endTime = performance.now();

			const time = endTime - startTime;

			results.push({
				boardType: scenario.name,
				time,
				wordsFound: words.length
			});

			// Performance should be reasonable for real-time use
			if (time > 100) {
				// 100ms threshold for real-time use
				return {
					name: 'Realistic Performance',
					passed: false,
					error: `Too slow for ${scenario.name}: ${time}ms`,
					details: results
				};
			}
		}

		const avgTime = results.reduce((sum, r) => sum + r.time, 0) / results.length;
		const avgWords = results.reduce((sum, r) => sum + r.wordsFound, 0) / results.length;

		return {
			name: 'Realistic Performance',
			passed: true,
			details: {
				averageTime: Math.round(avgTime * 100) / 100,
				averageWordsFound: Math.round(avgWords),
				results
			}
		};
	} catch (error) {
		return {
			name: 'Realistic Performance',
			passed: false,
			error: String(error)
		};
	}
}

/**
 * Test solver integration scenarios
 */
function testSolverIntegration(): IntegrationTestResult {
	try {
		const board = Board.createRandom();

		// Simulate solver usage pattern
		const solverResults = [];

		// Test different K values that solvers might use
		const kValues = [3, 10, 25, 50];

		for (const k of kValues) {
			const startTime = performance.now();
			const words = bestWordsFast(board, k);
			const endTime = performance.now();

			solverResults.push({
				k,
				time: endTime - startTime,
				wordsFound: words.length,
				topScore: words.length > 0 ? words[0].score : 0
			});
		}

		// Check that results are consistent (more K should not give worse top scores)
		for (let i = 1; i < solverResults.length; i++) {
			const prev = solverResults[i - 1];
			const curr = solverResults[i];

			// With more K, we should find at least as many words
			if (curr.wordsFound < prev.wordsFound && prev.wordsFound > 0) {
				return {
					name: 'Solver Integration',
					passed: false,
					error: `Inconsistent results: K=${curr.k} found fewer words than K=${prev.k}`,
					details: solverResults
				};
			}
		}

		return {
			name: 'Solver Integration',
			passed: true,
			details: solverResults
		};
	} catch (error) {
		return {
			name: 'Solver Integration',
			passed: false,
			error: String(error)
		};
	}
}

/**
 * Test integration with optimization functions
 */
function testOptimizationIntegration(): IntegrationTestResult {
	try {
		const board = Board.createRandom();
		const remainingTurns = 3;

		// Test that bestWordsFast integrates properly with the new optimization functions
		const results: any = {};

		// Test accurate upper bound calculation
		try {
			const accurateBound = calculateAccurateUpperBound(board, remainingTurns, 5);
			results.accurateBound = accurateBound;

			if (typeof accurateBound !== 'number' || accurateBound < 0) {
				return {
					name: 'Optimization Integration',
					passed: false,
					error: `Invalid accurate upper bound: ${accurateBound}`
				};
			}
		} catch (error) {
			return {
				name: 'Optimization Integration',
				passed: false,
				error: `Accurate upper bound calculation failed: ${error}`
			};
		}

		// Test different upper bound strategies
		try {
			const fastBound = calculateUpperBoundWithStrategy(
				board,
				remainingTurns,
				UpperBoundStrategy.FAST
			);
			const balancedBound = calculateUpperBoundWithStrategy(
				board,
				remainingTurns,
				UpperBoundStrategy.BALANCED
			);
			const accurateBound2 = calculateUpperBoundWithStrategy(
				board,
				remainingTurns,
				UpperBoundStrategy.ACCURATE
			);

			results.fastBound = fastBound;
			results.balancedBound = balancedBound;
			results.accurateBound2 = accurateBound2;

			// All bounds should be non-negative numbers
			const bounds = [fastBound, balancedBound, accurateBound2];
			for (const bound of bounds) {
				if (typeof bound !== 'number' || bound < 0) {
					return {
						name: 'Optimization Integration',
						passed: false,
						error: `Invalid bound value: ${bound}`
					};
				}
			}

			// Accurate bound should generally be <= other bounds (more precise)
			// But this isn't guaranteed, so we just check they're reasonable
		} catch (error) {
			return {
				name: 'Optimization Integration',
				passed: false,
				error: `Upper bound strategy calculation failed: ${error}`
			};
		}

		// Test that bestWordsFast results are consistent with optimization expectations
		try {
			const words = bestWordsFast(board, 10);
			if (words.length > 0) {
				const bestScore = words[0].score;
				const accurateBound = results.accurateBound;

				// The actual best word score should be <= the upper bound for a single turn
				// (This is a basic sanity check)
				if (bestScore > accurateBound && accurateBound > 0) {
					console.log(
						`Note: Best word score (${bestScore}) exceeds single-turn upper bound (${accurateBound}). This may be acceptable.`
					);
				}
			}
		} catch (error) {
			return {
				name: 'Optimization Integration',
				passed: false,
				error: `bestWordsFast consistency check failed: ${error}`
			};
		}

		return {
			name: 'Optimization Integration',
			passed: true,
			details: results
		};
	} catch (error) {
		return {
			name: 'Optimization Integration',
			passed: false,
			error: String(error)
		};
	}
}

/**
 * Run all integration tests
 */
export function runIntegrationTests(): IntegrationTestResult[] {
	console.log('🔗 Running bestWordsFast integration tests...\n');

	const tests = [
		testWordObjectStructure,
		testGameStateIntegration,
		testRealisticPerformance,
		testSolverIntegration,
		testOptimizationIntegration
	];

	const results: IntegrationTestResult[] = [];

	for (const test of tests) {
		const testName = test.name || 'Unknown Test';
		console.log(`Running ${testName}...`);

		try {
			const result = test();
			results.push(result);

			if (result.passed) {
				console.log(`✅ ${result.name} passed`);
			} else {
				console.log(`❌ ${result.name} failed: ${result.error}`);
			}

			if (result.details) {
				console.log(`   Details:`, result.details);
			}
		} catch (error) {
			console.log(`❌ ${testName} crashed: ${error}`);
			results.push({
				name: testName,
				passed: false,
				error: String(error)
			});
		}
		console.log();
	}

	const passedCount = results.filter((r) => r.passed).length;
	console.log(`\n📊 Integration Test Results: ${passedCount}/${results.length} tests passed`);

	return results;
}

// Export individual test functions for external use
export {
	testWordObjectStructure,
	testGameStateIntegration,
	testRealisticPerformance,
	testSolverIntegration,
	testOptimizationIntegration
};
